import sqlite3

conn = sqlite3.connect('skills_test.db')
c = conn.cursor()
c.execute('''CREATE TABLE IF NOT EXISTS users
             (id INTEGER PRIMARY KEY AUTOINCREMENT,
              username TEXT UNIQUE,
              password TEXT)''')

c.execute('''CREATE TABLE IF NOT EXISTS students
             (id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT,
              email TEXT)''')

c.execute('''CREATE TABLE IF NOT EXISTS skills_tests
             (id INTEGER PRIMARY KEY AUTOINCREMENT,
              test_name TEXT,
              description TEXT)''')
c.execute('''CREATE TABLE IF NOT EXISTS test_scores
             (id INTEGER PRIMARY KEY AUTOINCREMENT,
              student_id INTEGER,
              test_id INTEGER,
              score INTEGER,
              FOREIGN KEY (test_id) REFERENCES skills_tests(id),
              FOREIGN KEY (student_id) REFERENCES students(id))''')
c.execute("INSERT INTO users (username, password) VALUES(?,?)", ("admin", "admin123" ))
conn.commit()
conn.close()
print("Database initialized.")