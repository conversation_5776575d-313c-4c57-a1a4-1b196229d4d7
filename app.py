from flask import Flask, render_template, request, redirect, session
import sqlite3

app = Flask(__name__)
app.secret_key = "0ceed81ee513071e8491ba4e6b0fcd063744706ed965bf85bbf37773f627af4b"

def get_db_connection():
    conn = sqlite3.connect('skills_test.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ? AND password = ?', (username, password)).fetchone()
        conn.close()
        if user:
            session['user_id'] = user['id']
            session['username'] = user['username']
            return redirect('/dashboard')
        else:
            return 'Invalid username or password'
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect('/')
    return render_template ('dashboard.html', username=session['username'])

@app.route('/logout')
def logout():
    session.clear()
    return redirect('/')

@app.route('/students', methods=['GET', 'POST'])
def students():
    if 'user_id' not in session:
        return redirect('/')
    conn = get_db_connection()
    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        conn = get_db_connection()
        conn.execute('INSERT INTO students (name, email) VALUES (?, ?)', (name, email))
        conn.commit()
    students = conn.execute('SELECT * FROM students').fetchfall()
    conn.close()
    return render_template('students.html', students=students)

# Route: Add Scores
@app.route('/scores', methods=['GET', 'POST'])
def scores():
    if 'user_id' not in session:
        return redirect('/')
    
    conn = get_db_connection()
    students = conn.execute('SELECT * FROM students').fetchall()
    tests = conn.execute('SELECT * FROM skills_tests').fetchall()

    if request.method == 'POST':
        student_id = request.form['student_id']
        test_id = request.form['test_id']
        score = int(request.form['score'])
        conn.execute('INSERT INTO test_scores (student_id, test_id, score) VALUES (?, ?, ?)',
                     (student_id, test_id, score))
        conn.commit()

    scores = conn.execute('''
        SELECT s.name, t.test_name, sc.score
        FROM test_scores sc
        JOIN students s ON sc.student_id = s.id
        JOIN skills_tests t ON sc.test_id = t.id
    ''').fetchall()
    conn.close()

    return render_template('scores.html', students=students, tests=tests, scores=scores)
@app.route('/add_test', methods=['GET', 'POST'])
def add_test():
    if 'user_id' not in session:
        return redirect('/')
    
    conn = get_db_connection()
    if request.method == 'POST':
        name = request.form['name']
        desc = request.form['description']
        conn.execute('INSERT INTO skills_tests (test_name, description) VALUES (?, ?)', (name, desc))
        conn.commit()

    tests = conn.execute('SELECT * FROM skills_tests').fetchall()
    conn.close()
    return render_template('add_test.html', tests=tests)

@app.route('/report')
def report():
    if 'user_id' not in session:
        return redirect('/')
    
    conn = get_db_connection()
    report_data = conn.execute('''
        SELECT s.name, AVG(sc.score) as avg_score
        FROM test_scores sc
        JOIN students s ON sc.student_id = s.id
        GROUP BY s.id
    ''').fetchall()
    conn.close()

    return render_template('report.html', report=report_data)

if __name__ == '__main__':
    app.run(debug=True)
    